﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyAdressManager : ICompanyAdressService
    {
        ICompanyAdressDal _companyAdressDal;

        public CompanyAdressManager(ICompanyAdressDal companyAdress)
        {
            _companyAdressDal = companyAdress;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        public IResult Add(CompanyAdress companyAdress)
        {
            _companyAdressDal.Add(companyAdress);
            return new SuccessResult(Messages.CompanyAdressAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            _companyAdressDal.Delete(id);
            return new SuccessResult(Messages.CompanyAdressDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyAdress>> GetAll()
        {
            // Sadece Owner (sistem sahibi) tüm şirketlerin adreslerini görebilir
            return new SuccessDataResult<List<CompanyAdress>>(_companyAdressDal.GetAll());
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        public IResult Update(CompanyAdress companyAdress)
        {
            _companyAdressDal.Update(companyAdress);
            return new SuccessResult(Messages.CompanyAdressUpdated);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyAdressDetailDto>> GetCompanyAdressDetails()
        {
            // Sadece Owner (sistem sahibi) tüm şirketlerin adres detaylarını görebilir
            return new SuccessDataResult<List<CompanyAdressDetailDto>>(_companyAdressDal.GetCompanyAdressDetails());
        }

    }
}
