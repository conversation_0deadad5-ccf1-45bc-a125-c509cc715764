﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class CompanyUserManager : ICompanyUserService
    {
        ICompanyUserDal _companyUserDal;

        public CompanyUserManager(ICompanyUserDal companyUserDal)
        {
            _companyUserDal = companyUserDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Add(CompanyUser companyUser)
        {
            _companyUserDal.Add(companyUser);
            return new SuccessResult(Messages.UserAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        public IResult Delete(int id)
        {
            _companyUserDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyUser>> GetAll()
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(), Messages.CompanyUserGetAll);
        }
        [SecuredOperation("owner")]
        public IDataResult<List<CompanyUser>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(c => c.CityID == cityId));

        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails()
        {
            return new SuccessDataResult<List<CompanyUserDetailDto>>(_companyUserDal.GetCompanyUserDetails());
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyDetailDto>> GetCompanyDetails()
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyDetails());
        }
        [SecuredOperation("owner")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyUserDetailsByCityId(cityId));
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Update(CompanyUser companyUser)
        {
            _companyUserDal.Update(companyUser);
            return new SuccessResult(Messages.UserUpdated);
        }

        // Yeni eklenen metodlar
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<CompanyUser> GetById(int companyUserID)
        {
            var companyUser = _companyUserDal.Get(cu => cu.CompanyUserID == companyUserID);
            if (companyUser == null)
            {
                return new ErrorDataResult<CompanyUser>("Şirket kullanıcısı bulunamadı");
            }
            return new SuccessDataResult<CompanyUser>(companyUser);
        }

        // Salon soft delete (sadece temel tablolar)
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult SoftDeleteCompanyUser(int companyUserID)
        {
            var result = _companyUserDal.SoftDeleteCompanyUserBasic(companyUserID);
            if (result.Success)
            {
                 return new SuccessResult("Salon başarıyla pasif hale getirildi. Pasif salonlar bölümünden geri yükleyebilirsiniz.");
            }
            return new ErrorResult(result.Message);
        }

        // Silinen salonları listeleme
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<DeletedCompanyUserDto>> GetDeletedCompanyUsers()
        {
            var result = _companyUserDal.GetDeletedCompanyUsers();
            return new SuccessDataResult<List<DeletedCompanyUserDto>>(result, "Pasif salonlar listelendi");
        }

        // Salon geri yükleme
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RestoreCompanyUser(int companyUserID)
        {
            var result = _companyUserDal.RestoreCompanyUserBasic(companyUserID);
            if (result.Success)
            {
                return new SuccessResult("Salon başarıyla geri yüklendi.");
            }
            return new ErrorResult(result.Message);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<CompanyUserFullDetailDto> GetCompanyUserFullDetails(int companyUserID)
        {
            var result = _companyUserDal.GetCompanyUserFullDetails(companyUserID);
            if (result != null)
            {
                return new SuccessDataResult<CompanyUserFullDetailDto>(result);
            }
            return new ErrorDataResult<CompanyUserFullDetailDto>("Kullanıcı detayları bulunamadı");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedCompanyUserDto> GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "")
        {
            var result = _companyUserDal.GetCompanyUsersPaginated(pageNumber, pageSize, searchTerm);
            if (result != null)
            {
                return new SuccessDataResult<PaginatedCompanyUserDto>(result);
            }
            return new ErrorDataResult<PaginatedCompanyUserDto>("Sayfalanmış veriler alınamadı");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto)
        {
            var result = _companyUserDal.UpdateCompanyUserFull(updateDto);
            return result;
        }


    }
}
