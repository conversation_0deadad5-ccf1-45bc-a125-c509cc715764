﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;


namespace Business.Concrete
{
    public class TransactionManager : ITransactionService
    {
        private readonly ITransactionDal _transactionDal;
        private readonly IMemberDal _memberDal;
        private readonly ICompanyContext _companyContext;

        public TransactionManager(ITransactionDal transactionDal, IMemberDal memberDal, ICompanyContext companyContext)
        {
            _transactionDal = transactionDal;
            _memberDal = memberDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult AddBulk(BulkTransactionDto bulkTransaction)
        {
            // SOLID prensiplerine uygun: Karmaşık iş mantığı DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.AddBulkTransactionWithBusinessLogic(bulkTransaction, companyId);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Add(Transaction transaction)
        {
            // SOLID prensiplerine uygun: Karmaşık iş mantığı DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.AddTransactionWithBusinessLogic(transaction, companyId);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult UpdatePaymentStatus(int transactionId)
        {
            // SOLID prensiplerine uygun: Karmaşık iş mantığı DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.UpdatePaymentStatusWithBusinessLogic(transactionId, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult UpdateAllPaymentStatus(int memberId)
        {
            // SOLID prensiplerine uygun: Karmaşık iş mantığı DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.UpdateAllPaymentStatusWithBusinessLogic(memberId, companyId);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<Transaction>> GetAll()
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<Transaction>> GetByMemberId(int memberId)
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll(t => t.MemberID == memberId));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<TransactionDetailDto>> GetTransactionsWithDetails()
        {
            return new SuccessDataResult<List<TransactionDetailDto>>(_transactionDal.GetTransactionsWithDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<TransactionDetailDto>> GetUnpaidTransactions(int memberId)
        {
            // SOLID prensiplerine uygun: Filtreleme işlemini DAL katmanına taşıdık
            var transactions = _transactionDal.GetUnpaidTransactionsByMemberId(memberId);
            return new SuccessDataResult<List<TransactionDetailDto>>(transactions);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int transactionId)
        {
            // SOLID prensiplerine uygun: Validation ve soft delete logic'i DAL katmanına taşıdık
            return _transactionDal.SoftDeleteTransactionWithValidation(transactionId, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<decimal> GetMonthlyTransactionTotal(int year, int month)
        {
            // SOLID prensiplerine uygun: Tarih hesaplama ve filtreleme işlemini DAL katmanına taşıdık
            var totalAmount = _transactionDal.GetMonthlyTransactionTotal(year, month);
            return new SuccessDataResult<decimal>(totalAmount, "Aylık işlem toplamı başarıyla getirildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<decimal> GetDailyTransactionTotal(DateTime date)
        {
            // SOLID prensiplerine uygun: Tarih hesaplama ve filtreleme işlemini DAL katmanına taşıdık
            var totalAmount = _transactionDal.GetDailyTransactionTotal(date);
            return new SuccessDataResult<decimal>(totalAmount, "Günlük işlem toplamı başarıyla getirildi.");
        }
    }
}