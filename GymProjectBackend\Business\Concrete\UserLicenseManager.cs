﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class UserLicenseManager : IUserLicenseService
    {
        private readonly IUserLicenseDal _userLicenseDal;

        public UserLicenseManager(IUserLicenseDal userLicenseDal)
        {
            _userLicenseDal = userLicenseDal;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Add(UserLicense userLicense)
        {
            // SOLID prensiplerine uygun: Tarih yönetimi DAL katmanına taşıdık
            return _userLicenseDal.AddUserLicenseWithDateManagement(userLicense);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        public IResult Delete(int id)
        {
            _userLicenseDal.Delete(id);
            return new SuccessResult("Kullanıcı lisansı başarıyla silindi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<UserLicenseDto>> GetAll()
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetUserLicenseDetails());
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedUserLicenseDto> GetAllPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax)
        {
            var result = _userLicenseDal.GetUserLicenseDetailsPaginated(page, pageSize, searchTerm, sortBy, companyName, remainingDaysMin, remainingDaysMax);
            return new SuccessDataResult<PaginatedUserLicenseDto>(result);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedUserLicenseDto> GetExpiredAndPassive(int page, int pageSize, string searchTerm)
        {
            var result = _userLicenseDal.GetExpiredAndPassiveLicenses(page, pageSize, searchTerm);
            return new SuccessDataResult<PaginatedUserLicenseDto>(result);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<UserLicenseDto>> GetActiveByUserId(int userId)
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetActiveUserLicensesByUserId(userId));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<UserLicenseDto>> GetMyActiveLicenses(int userId)
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetActiveUserLicensesByUserId(userId));
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<UserLicenseDto> GetById(int id)
        {
            return new SuccessDataResult<UserLicenseDto>(_userLicenseDal.GetUserLicenseDetail(id));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Update(UserLicense userLicense)
        {
            // SOLID prensiplerine uygun: Tarih yönetimi DAL katmanına taşıdık
            return _userLicenseDal.UpdateUserLicenseWithDateManagement(userLicense);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult Purchase(LicensePurchaseDto licensePurchaseDto)
        {
            return _userLicenseDal.PurchaseLicense(licensePurchaseDto);
        }



        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult ExtendLicenseByPackage(LicenseExtensionByPackageDto licenseExtensionByPackageDto)
        {
            return _userLicenseDal.ExtendLicenseByPackage(licenseExtensionByPackageDto);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RevokeLicense(int userLicenseId)
        {
            // SOLID prensiplerine uygun: Validation ve entity manipulation logic'i DAL katmanına taşıdık
            return _userLicenseDal.RevokeLicenseWithValidation(userLicenseId);
        }

        public IDataResult<List<string>> GetUserRoles(int userId)
        {
            var roles = _userLicenseDal.GetUserRoles(userId);
            return new SuccessDataResult<List<string>>(roles);
        }
    }
}