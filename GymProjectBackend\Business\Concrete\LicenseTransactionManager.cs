﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicenseTransactionManager : ILicenseTransactionService
    {
        private readonly ILicenseTransactionDal _licenseTransactionDal;

        public LicenseTransactionManager(ILicenseTransactionDal licenseTransactionDal)
        {
            _licenseTransactionDal = licenseTransactionDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Add(LicenseTransaction licenseTransaction)
        {
            return _licenseTransactionDal.AddLicenseTransaction(licenseTransaction);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetAll()
        {
            return _licenseTransactionDal.GetAllOrderedByDate();
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize)
        {
            return _licenseTransactionDal.GetAllFiltered(userID, startDate, endDate, page, pageSize);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<List<LicenseTransaction>> GetByUserId(int userId)
        {
            return new SuccessDataResult<List<LicenseTransaction>>(
                _licenseTransactionDal.GetAll(lt => lt.UserID == userId && lt.IsActive == true));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            return _licenseTransactionDal.SoftDeleteLicenseTransaction(id);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<object> GetTotals()
        {
            return _licenseTransactionDal.GetTotalAmountsByPaymentMethod();
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        public IDataResult<object> GetMonthlyRevenue(int year)
        {
            return _licenseTransactionDal.GetMonthlyRevenueByYear(year);
        }
    }
}