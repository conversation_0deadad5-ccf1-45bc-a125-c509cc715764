﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class ProductManager : IProductService
    {
        IProductDal _productDal;
        private readonly ICompanyContext _companyContext;

        public ProductManager(IProductDal productDal, ICompanyContext companyContext)
        {
            _productDal = productDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Add(Product product)
        {
            _productDal.Add(product);
            return new SuccessResult(Messages.ProductAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        public IResult Delete(int productId)
        {
            // SOLID prensiplerine uygun: Soft delete işlemini DAL katmanına devredildi
            return _productDal.SoftDeleteProduct(productId, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Product>> GetAll()
        {
            return new SuccessDataResult<List<Product>>(_productDal.GetAll(m => m.IsActive == true));

        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Product>> GetAllPaginated(ProductPagingParameters parameters)
        {
            var result = _productDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<Product>>(result, Messages.ProductsListed);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<Product> GetById(int productId)
        {
            // SOLID prensiplerine uygun: Validation logic'i DAL katmanına taşıdık
            return _productDal.GetProductByIdWithValidation(productId, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Update(Product product)
        {
            // SOLID prensiplerine uygun: Complex business logic DAL katmanına devredildi
            return _productDal.UpdateProductWithBusinessLogic(product, _companyContext.GetCompanyId());
        }
    }
}
