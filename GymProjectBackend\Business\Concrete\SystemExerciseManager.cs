using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class SystemExerciseManager : ISystemExerciseService
    {
        ISystemExerciseDal _systemExerciseDal;

        public SystemExerciseManager(ISystemExerciseDal systemExerciseDal)
        {
            _systemExerciseDal = systemExerciseDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetAllSystemExercises()
        {
            var result = _systemExerciseDal.GetAllSystemExercises();
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> GetSystemExercisesByCategory(int categoryId)
        {
            var result = _systemExerciseDal.GetSystemExercisesByCategory(categoryId);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<SystemExerciseDto>> GetSystemExercisesFiltered(SystemExerciseFilterDto filter)
        {
            var result = _systemExerciseDal.GetSystemExercisesFiltered(filter);
            return new SuccessDataResult<PaginatedResult<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<SystemExerciseDto>> SearchSystemExercises(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<SystemExerciseDto>>("Arama terimi boş olamaz.");
            }

            var result = _systemExerciseDal.SearchSystemExercises(searchTerm);
            return new SuccessDataResult<List<SystemExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<SystemExerciseDto> GetSystemExerciseDetail(int exerciseId)
        {
            var result = _systemExerciseDal.GetSystemExerciseDetail(exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }

            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<SystemExerciseDto> GetById(int exerciseId)
        {
            // SOLID prensiplerine uygun: Karmaşık DTO mapping DAL katmanına devredildi
            var result = _systemExerciseDal.GetSystemExerciseDetail(exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<SystemExerciseDto>("Sistem egzersizi bulunamadı.");
            }

            return new SuccessDataResult<SystemExerciseDto>(result);
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi ekleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(SystemExerciseAddDto exerciseAddDto)
        {
            // SOLID prensiplerine uygun: Entity oluşturma ve tarih yönetimi DAL katmanına taşıdık
            return _systemExerciseDal.AddSystemExerciseWithManagement(exerciseAddDto);
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi güncelleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(SystemExerciseUpdateDto exerciseUpdateDto)
        {
            // SOLID prensiplerine uygun: Entity güncelleme ve tarih yönetimi DAL katmanına taşıdık
            return _systemExerciseDal.UpdateSystemExerciseWithManagement(exerciseUpdateDto);
        }

        [SecuredOperation("owner")] // Sadece owner sistem egzersizi silebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int exerciseId)
        {
            // SOLID prensiplerine uygun: Validation ve soft delete logic'i DAL katmanına taşıdık
            return _systemExerciseDal.SoftDeleteSystemExerciseWithValidation(exerciseId);
        }
    }
}
